==> 2025-05-24 21:42:25 <==
# cmd: D:\ProgramData\anaconda3\Scripts\conda-script.py create -n index-tts python=3.10
# conda version: 24.9.2
+defaults/noarch::pip-25.1-pyhc872135_2
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.2.25-haa95532_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-3.0.16-h3f729d1_0
+defaults/win-64::python-3.10.16-h4607a30_1
+defaults/win-64::setuptools-78.1.1-py310haa95532_0
+defaults/win-64::sqlite-3.45.3-h2bbff1b_0
+defaults/win-64::tk-8.6.14-h0416ee5_0
+defaults/win-64::vc-14.42-haa95532_5
+defaults/win-64::vs2015_runtime-14.42.34433-hbfb602d_5
+defaults/win-64::wheel-0.45.1-py310haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python=3.10']
==> 2025-05-24 21:45:57 <==
# cmd: D:\ProgramData\anaconda3\Scripts\conda-script.py install -c conda-forge pynini==2.1.5
# conda version: 24.9.2
-defaults/win-64::ca-certificates-2025.2.25-haa95532_0
-defaults/win-64::openssl-3.0.16-h3f729d1_0
-defaults/win-64::vs2015_runtime-14.42.34433-hbfb602d_5
+conda-forge/noarch::ca-certificates-2025.4.26-h4c7d964_0
+conda-forge/noarch::font-ttf-dejavu-sans-mono-2.37-hab24e00_0
+conda-forge/noarch::font-ttf-inconsolata-3.000-h77eed37_0
+conda-forge/noarch::font-ttf-source-code-pro-2.038-h77eed37_0
+conda-forge/noarch::font-ttf-ubuntu-0.83-h77eed37_3
+conda-forge/noarch::fonts-conda-ecosystem-1-0
+conda-forge/noarch::fonts-conda-forge-1-0
+conda-forge/win-64::dlfcn-win32-1.4.1-h63175ca_0
+conda-forge/win-64::expat-2.7.0-he0c23c2_0
+conda-forge/win-64::fribidi-1.0.10-h8d14728_0
+conda-forge/win-64::getopt-win32-0.1-hcfcfb64_1
+conda-forge/win-64::graphite2-1.3.13-h63175ca_1003
+conda-forge/win-64::graphviz-7.1.0-h51cb2cd_0
+conda-forge/win-64::gts-0.7.6-h6b5321d_4
+conda-forge/win-64::harfbuzz-6.0.0-h196d34a_1
+conda-forge/win-64::icu-72.1-h63175ca_0
+conda-forge/win-64::jpeg-9e-hcfcfb64_3
+conda-forge/win-64::lerc-4.0.0-h6470a55_1
+conda-forge/win-64::libdeflate-1.22-h2466b09_0
+conda-forge/win-64::libexpat-2.7.0-he0c23c2_0
+conda-forge/win-64::libiconv-1.18-h135ad9c_1
+conda-forge/win-64::libwebp-1.5.0-h3b0e114_0
+conda-forge/win-64::libwebp-base-1.5.0-h3b0e114_0
+conda-forge/win-64::lz4-c-1.9.4-hcfcfb64_0
+conda-forge/win-64::openfst-1.8.2-h91493d7_2
+conda-forge/win-64::openssl-3.5.0-ha4e3fda_1
+conda-forge/win-64::pango-1.50.12-hdffb7b3_1
+conda-forge/win-64::pixman-0.46.0-had0cd8c_0
+conda-forge/win-64::pynini-2.1.5-py310h232114e_6
+conda-forge/win-64::python_abi-3.10-2_cp310
+conda-forge/win-64::ucrt-10.0.22621.0-h57928b3_1
+conda-forge/win-64::vc14_runtime-14.42.34438-hfd919c2_26
+conda-forge/win-64::vs2015_runtime-14.42.34438-h7142326_26
+defaults/win-64::cairo-1.16.0-hc68a040_5
+defaults/win-64::fontconfig-2.14.1-hb33846d_3
+defaults/win-64::freeglut-3.4.0-hd77b12b_0
+defaults/win-64::freetype-2.13.3-h0620614_0
+defaults/win-64::glib-2.78.4-hd77b12b_0
+defaults/win-64::glib-tools-2.78.4-hd77b12b_0
+defaults/win-64::libgd-2.3.3-hd9a01a5_4
+defaults/win-64::libglib-2.78.4-ha17d25a_0
+defaults/win-64::libpng-1.6.39-h8cc25b3_0
+defaults/win-64::libtiff-4.7.0-h404307b_0
+defaults/win-64::libxml2-2.13.8-h866ff63_0
+defaults/win-64::pcre2-10.42-h0ff8eda_1
+defaults/win-64::zstd-1.5.6-h8880b57_0
# update specs: ['pynini==2.1.5']
