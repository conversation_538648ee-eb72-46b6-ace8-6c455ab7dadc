{"build": "hc68a040_5", "build_number": 5, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["glib", "vc >=14.2,<15.0a0", "vs2015_runtime >=14.29.30133,<15.0a0", "zlib >=1.2.13,<1.3.0a0", "freetype >=2.10.4,<3.0a0", "libpng >=1.6.39,<1.7.0a0", "libglib >=2.78.4,<3.0a0", "fontconfig >=2.14.1,<3.0a0", "pixman >=0.40.0,<1.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\.conda\\pkgs\\cairo-1.16.0-hc68a040_5", "files": ["Library/bin/cairo-gobject.dll", "Library/bin/cairo.dll", "Library/include/cairo/cairo-deprecated.h", "Library/include/cairo/cairo-features.h", "Library/include/cairo/cairo-ft.h", "Library/include/cairo/cairo-gobject.h", "Library/include/cairo/cairo-pdf.h", "Library/include/cairo/cairo-ps.h", "Library/include/cairo/cairo-script.h", "Library/include/cairo/cairo-svg.h", "Library/include/cairo/cairo-version.h", "Library/include/cairo/cairo-win32.h", "Library/include/cairo/cairo.h", "Library/lib/cairo-gobject.lib", "Library/lib/cairo-static.lib", "Library/lib/cairo.lib", "Library/lib/pkgconfig/cairo-gobject.pc", "Library/lib/pkgconfig/cairo-pdf.pc", "Library/lib/pkgconfig/cairo-png.pc", "Library/lib/pkgconfig/cairo-ps.pc", "Library/lib/pkgconfig/cairo-svg.pc", "Library/lib/pkgconfig/cairo-win32.pc", "Library/lib/pkgconfig/cairo.pc"], "fn": "cairo-1.16.0-hc68a040_5.tar.bz2", "license": "LGPL-2.1-or-later or MPL-1.1", "link": {"source": "C:\\Users\\<USER>\\.conda\\pkgs\\cairo-1.16.0-hc68a040_5", "type": 1}, "md5": "c36c4d952b1dfa8d12e136c06343bd3b", "name": "cairo", "package_tarball_full_path": "C:\\Users\\<USER>\\.conda\\pkgs\\cairo-1.16.0-hc68a040_5.tar.bz2", "paths_data": {"paths": [{"_path": "Library/bin/cairo-gobject.dll", "path_type": "hardlink", "sha256": "3d3f40b58670923d07edc6310ba593b4799280788bac71b9263cf02bb0cbe914", "sha256_in_prefix": "3d3f40b58670923d07edc6310ba593b4799280788bac71b9263cf02bb0cbe914", "size_in_bytes": 27136}, {"_path": "Library/bin/cairo.dll", "path_type": "hardlink", "sha256": "2d4e580f6a2cad92deeac01a4928a2cb7a8115050326455e895801372f75c99c", "sha256_in_prefix": "2d4e580f6a2cad92deeac01a4928a2cb7a8115050326455e895801372f75c99c", "size_in_bytes": 1413632}, {"_path": "Library/include/cairo/cairo-deprecated.h", "path_type": "hardlink", "sha256": "873654913917ec9623c0d6f4a940f8ea638c0954cd2c01471df570046bea2bbf", "sha256_in_prefix": "873654913917ec9623c0d6f4a940f8ea638c0954cd2c01471df570046bea2bbf", "size_in_bytes": 8698}, {"_path": "Library/include/cairo/cairo-features.h", "path_type": "hardlink", "sha256": "89393325884e9fb616183179d4757f73c5994f7fe829dd063e9664d41f5901d9", "sha256_in_prefix": "89393325884e9fb616183179d4757f73c5994f7fe829dd063e9664d41f5901d9", "size_in_bytes": 646}, {"_path": "Library/include/cairo/cairo-ft.h", "path_type": "hardlink", "sha256": "489f74a852339fc04eb11e26ddd22536e0147967ea67b301f6636e21bb7c15fa", "sha256_in_prefix": "489f74a852339fc04eb11e26ddd22536e0147967ea67b301f6636e21bb7c15fa", "size_in_bytes": 3721}, {"_path": "Library/include/cairo/cairo-gobject.h", "path_type": "hardlink", "sha256": "f8043a9f13867abaca4664a1263a38a19075f6387ca4ef3d7403db3764385d2c", "sha256_in_prefix": "f8043a9f13867abaca4664a1263a38a19075f6387ca4ef3d7403db3764385d2c", "size_in_bytes": 6452}, {"_path": "Library/include/cairo/cairo-pdf.h", "path_type": "hardlink", "sha256": "6ce2b0745821fe39bdccdef07e62672ee52ad51890f67132883af331972f8b05", "sha256_in_prefix": "6ce2b0745821fe39bdccdef07e62672ee52ad51890f67132883af331972f8b05", "size_in_bytes": 5617}, {"_path": "Library/include/cairo/cairo-ps.h", "path_type": "hardlink", "sha256": "2ca71c761c807eb16fb97099ef0f8c6b877c61f49e3f568075eb762837b2f610", "sha256_in_prefix": "2ca71c761c807eb16fb97099ef0f8c6b877c61f49e3f568075eb762837b2f610", "size_in_bytes": 3632}, {"_path": "Library/include/cairo/cairo-script.h", "path_type": "hardlink", "sha256": "03d8abe5e374e9b8b51598f1e1db29cc1f42cc5a52722338acaffe0218d9b6f2", "sha256_in_prefix": "03d8abe5e374e9b8b51598f1e1db29cc1f42cc5a52722338acaffe0218d9b6f2", "size_in_bytes": 3072}, {"_path": "Library/include/cairo/cairo-svg.h", "path_type": "hardlink", "sha256": "0e678606c9678afbac4001f0959d73f968b7d1849d2d973e1808ddc5a91e8e24", "sha256_in_prefix": "0e678606c9678afbac4001f0959d73f968b7d1849d2d973e1808ddc5a91e8e24", "size_in_bytes": 4504}, {"_path": "Library/include/cairo/cairo-version.h", "path_type": "hardlink", "sha256": "e65312e5051f2754c908d6965c2dc20c0d7a2f13be00fc1d0c3379635032c575", "sha256_in_prefix": "e65312e5051f2754c908d6965c2dc20c0d7a2f13be00fc1d0c3379635032c575", "size_in_bytes": 148}, {"_path": "Library/include/cairo/cairo-win32.h", "path_type": "hardlink", "sha256": "7fa8816a524f901eb88025304481b78ab4e367c39b6cedcce74ac7e33db37891", "sha256_in_prefix": "7fa8816a524f901eb88025304481b78ab4e367c39b6cedcce74ac7e33db37891", "size_in_bytes": 3789}, {"_path": "Library/include/cairo/cairo.h", "path_type": "hardlink", "sha256": "686c84be22a7a2d4a5b75aaf6b8c50292e11971a7a417f64d5a221d81a46c096", "sha256_in_prefix": "686c84be22a7a2d4a5b75aaf6b8c50292e11971a7a417f64d5a221d81a46c096", "size_in_bytes": 110955}, {"_path": "Library/lib/cairo-gobject.lib", "path_type": "hardlink", "sha256": "0ac1b3bbbf44e3fb651edced32fd6969ab2196fb1910dd576531acd6f6490e89", "sha256_in_prefix": "0ac1b3bbbf44e3fb651edced32fd6969ab2196fb1910dd576531acd6f6490e89", "size_in_bytes": 11382}, {"_path": "Library/lib/cairo-static.lib", "path_type": "hardlink", "sha256": "69bb2331b5fc5646161fa9347d75a177a387e8efe55ef9ffe1ffc61f0fce10c0", "sha256_in_prefix": "69bb2331b5fc5646161fa9347d75a177a387e8efe55ef9ffe1ffc61f0fce10c0", "size_in_bytes": 6103332}, {"_path": "Library/lib/cairo.lib", "path_type": "hardlink", "sha256": "f44efdf7edfa89da31dd1f4e95d125420371d82c2102f6903b0e90c48b1481b8", "sha256_in_prefix": "f44efdf7edfa89da31dd1f4e95d125420371d82c2102f6903b0e90c48b1481b8", "size_in_bytes": 100646}, {"_path": "Library/lib/pkgconfig/cairo-gobject.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "f399bed2b3ff9dd7f3524d1b180878490ad680a6cb639d0ea1e235861b6890b6", "sha256_in_prefix": "0cfebba528bf4295380f14ad439195c93a93617b28b922962c01fdf0d1f54827", "size_in_bytes": 335}, {"_path": "Library/lib/pkgconfig/cairo-pdf.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "96d651e30aee6ff5d11b737add03296547e10396653a2e0e0c1d196f5b791c25", "sha256_in_prefix": "e1b6715d99b19abb16c0f9c33cce3d1dfa67b7dfdf5fc73714ff6546ca3ab1cd", "size_in_bytes": 294}, {"_path": "Library/lib/pkgconfig/cairo-png.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "8063511befa0ab046dea3da530b066b0aec23ae167c2983c98e0334370d66910", "sha256_in_prefix": "90bedc93e934a175d430e1f3beff7b1a7aa3f45c585b4e2f9f3451a3938521cf", "size_in_bytes": 295}, {"_path": "Library/lib/pkgconfig/cairo-ps.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "a32f3f9fc09e73a0e5fdbdc2539f9aff504b871e906461386c0d1b1e392e65fc", "sha256_in_prefix": "73fee8db8a7a36ec535d889cc4b601a7785bb98625e3f86bc072b00b2633c4bf", "size_in_bytes": 300}, {"_path": "Library/lib/pkgconfig/cairo-svg.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "542ff8512493112589ff1d2b70f01cbfaf920405a6196d8ba9e281f39c037c0a", "sha256_in_prefix": "6cc34c8f49fedad74435395327da0e280ad68f117c8e97c292740a867efd0f56", "size_in_bytes": 294}, {"_path": "Library/lib/pkgconfig/cairo-win32.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "5361656ca20074507aa1cd9be393b47042d571ee5dfff5d26839ae23e6a8e385", "sha256_in_prefix": "eaa517bcfcb8321be49f9ae18f3da2def0294efcd9b5cc39a5d375b823f9d4c9", "size_in_bytes": 294}, {"_path": "Library/lib/pkgconfig/cairo.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:\\Users\\<USER>\\conda-bld\\cairo_1728989133456\\_h_env", "sha256": "7e30a85e47c128e4a6f5b5889bab34b4a70069dfa80da01e67336353d480fcd2", "sha256_in_prefix": "5501c5ce559ad65930abe88c5177c219a3510cc974c095f70d399db899d8638d", "size_in_bytes": 288}], "paths_version": 1}, "requested_spec": "None", "sha256": "6fbdfcf507de4168ddbc90b442cf8ebcdc94e013dacb7d165e12b09d931692e9", "size": 2372953, "subdir": "win-64", "timestamp": 1728989305000, "url": "https://repo.anaconda.com/pkgs/main/win-64/cairo-1.16.0-hc68a040_5.tar.bz2", "version": "1.16.0"}