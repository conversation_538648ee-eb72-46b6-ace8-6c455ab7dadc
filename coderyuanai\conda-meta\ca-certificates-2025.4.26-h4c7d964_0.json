{"build": "h4c7d964_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__win"], "extracted_package_dir": "C:\\Users\\<USER>\\.conda\\pkgs\\ca-certificates-2025.4.26-h4c7d964_0", "files": ["Library/ssl/cacert.pem", "Library/ssl/cert.pem"], "fn": "ca-certificates-2025.4.26-h4c7d964_0.conda", "license": "ISC", "link": {"source": "C:\\Users\\<USER>\\.conda\\pkgs\\ca-certificates-2025.4.26-h4c7d964_0", "type": 1}, "md5": "23c7fd5062b48d8294fc7f61bf157fba", "name": "ca-certificates", "noarch": "generic", "package_tarball_full_path": "C:\\Users\\<USER>\\.conda\\pkgs\\ca-certificates-2025.4.26-h4c7d964_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [{"_path": "Library/ssl/cacert.pem", "path_type": "hardlink", "sha256": "2b7b1026f18a297e2148189ca0cbe7d3e7f9efc36f7231ccc2820a404feb5596", "sha256_in_prefix": "2b7b1026f18a297e2148189ca0cbe7d3e7f9efc36f7231ccc2820a404feb5596", "size_in_bytes": 283771}, {"_path": "Library/ssl/cert.pem", "path_type": "hardlink", "sha256": "2b7b1026f18a297e2148189ca0cbe7d3e7f9efc36f7231ccc2820a404feb5596", "sha256_in_prefix": "2b7b1026f18a297e2148189ca0cbe7d3e7f9efc36f7231ccc2820a404feb5596", "size_in_bytes": 283771}], "paths_version": 1}, "requested_spec": "None", "sha256": "1454f3f53a3b828d3cb68a3440cb0fa9f1cc0e3c8c26e9e023773dc19d88cc06", "size": 152945, "subdir": "noarch", "timestamp": 1745653639000, "url": "https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.4.26-h4c7d964_0.conda", "version": "2025.4.26"}