{"build": "hd77b12b_0", "build_number": 0, "channel": "https://repo.anaconda.com/pkgs/main/win-64", "constrains": [], "depends": ["vc >=14.1,<15.0a0", "vs2015_runtime >=14.16.27012,<15.0a0"], "extracted_package_dir": "C:\\Users\\<USER>\\.conda\\pkgs\\freeglut-3.4.0-hd77b12b_0", "files": ["Library/bin/freeglut.dll", "Library/bin/glut.dll", "Library/include/GL/freeglut.h", "Library/include/GL/freeglut_ext.h", "Library/include/GL/freeglut_std.h", "Library/include/GL/freeglut_ucall.h", "Library/include/GL/glut.h", "Library/lib/cmake/FreeGLUT/FreeGLUTConfig.cmake", "Library/lib/cmake/FreeGLUT/FreeGLUTConfigVersion.cmake", "Library/lib/cmake/FreeGLUT/FreeGLUTTargets-release.cmake", "Library/lib/cmake/FreeGLUT/FreeGLUTTargets.cmake", "Library/lib/freeglut.lib", "Library/lib/glut.lib", "Library/lib/pkgconfig/glut.pc"], "fn": "freeglut-3.4.0-hd77b12b_0.conda", "license": "MIT", "link": {"source": "C:\\Users\\<USER>\\.conda\\pkgs\\freeglut-3.4.0-hd77b12b_0", "type": 1}, "md5": "27520f8cb289e2520b44cbe1fd756feb", "name": "freeglut", "package_tarball_full_path": "C:\\Users\\<USER>\\.conda\\pkgs\\freeglut-3.4.0-hd77b12b_0.conda", "paths_data": {"paths": [{"_path": "Library/bin/freeglut.dll", "path_type": "hardlink", "sha256": "e1c2c28a66e2777b1c9afa21d498715c88a44689f76d13769ddd13ab6c748a60", "sha256_in_prefix": "e1c2c28a66e2777b1c9afa21d498715c88a44689f76d13769ddd13ab6c748a60", "size_in_bytes": 269592}, {"_path": "Library/bin/glut.dll", "path_type": "hardlink", "sha256": "2ff0bb069c3f6db78abeb1dcb989e3d9e31164b9f75639706c38daf826b29cad", "sha256_in_prefix": "2ff0bb069c3f6db78abeb1dcb989e3d9e31164b9f75639706c38daf826b29cad", "size_in_bytes": 269592}, {"_path": "Library/include/GL/freeglut.h", "path_type": "hardlink", "sha256": "e5a823629d7b4ed8f3ec9497161ae2765bf5f13e32bb7928f6f1bf8eef2a3109", "sha256_in_prefix": "e5a823629d7b4ed8f3ec9497161ae2765bf5f13e32bb7928f6f1bf8eef2a3109", "size_in_bytes": 681}, {"_path": "Library/include/GL/freeglut_ext.h", "path_type": "hardlink", "sha256": "af00415f9ca3aa9f09163a686faf1ca402220642900cd489797a036102f526f0", "sha256_in_prefix": "af00415f9ca3aa9f09163a686faf1ca402220642900cd489797a036102f526f0", "size_in_bytes": 11174}, {"_path": "Library/include/GL/freeglut_std.h", "path_type": "hardlink", "sha256": "522c6f65a461b2fc12be7897b149b67da213140808c3704a1ed0d4fd495526b5", "sha256_in_prefix": "522c6f65a461b2fc12be7897b149b67da213140808c3704a1ed0d4fd495526b5", "size_in_bytes": 27097}, {"_path": "Library/include/GL/freeglut_ucall.h", "path_type": "hardlink", "sha256": "27ddc012acc455901262a9b5edeffff39d5109da17f3d445b006549b47882d53", "sha256_in_prefix": "27ddc012acc455901262a9b5edeffff39d5109da17f3d445b006549b47882d53", "size_in_bytes": 5871}, {"_path": "Library/include/GL/glut.h", "path_type": "hardlink", "sha256": "e3ffcfd1147cd45a9ddb49ab9791e8fc6269f52732f0c02f10f2daf9cc0e730b", "sha256_in_prefix": "e3ffcfd1147cd45a9ddb49ab9791e8fc6269f52732f0c02f10f2daf9cc0e730b", "size_in_bytes": 639}, {"_path": "Library/lib/cmake/FreeGLUT/FreeGLUTConfig.cmake", "path_type": "hardlink", "sha256": "27800d5fe85e51dc9ad2c7ab2aaccb5cc180b6d640c2232916e8181c9c5697ca", "sha256_in_prefix": "27800d5fe85e51dc9ad2c7ab2aaccb5cc180b6d640c2232916e8181c9c5697ca", "size_in_bytes": 60}, {"_path": "Library/lib/cmake/FreeGLUT/FreeGLUTConfigVersion.cmake", "path_type": "hardlink", "sha256": "293a93b0e0682fe02988ec1f8da54d93a07dc30dfd7c06192e579d5eab50ba52", "sha256_in_prefix": "293a93b0e0682fe02988ec1f8da54d93a07dc30dfd7c06192e579d5eab50ba52", "size_in_bytes": 1904}, {"_path": "Library/lib/cmake/FreeGLUT/FreeGLUTTargets-release.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_2et_101mpm/croot/freeglut_1715706044065/_h_env", "sha256": "851c2279554c0df8f0ee83ca7d6f2051d61f406c06e3808d2541e1fff1f05196", "sha256_in_prefix": "e01b3b7ee846f24b4186a05034a960092cd35bf6791b0d86a5064c9460f7b9a4", "size_in_bytes": 1010}, {"_path": "Library/lib/cmake/FreeGLUT/FreeGLUTTargets.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_2et_101mpm/croot/freeglut_1715706044065/_h_env", "sha256": "5dafd77587393ad59305ae68be5aa92ff91e0962d0f37d59ffa25209e539e89d", "sha256_in_prefix": "53a2ab9ac5f5013cc0aa4b27120f331ce5b456b819880206c74a5470d6148436", "size_in_bytes": 3953}, {"_path": "Library/lib/freeglut.lib", "path_type": "hardlink", "sha256": "6e403161a3104893bd09ffa4a3d30527bf8da36c0db35521965721bf358f1f8f", "sha256_in_prefix": "6e403161a3104893bd09ffa4a3d30527bf8da36c0db35521965721bf358f1f8f", "size_in_bytes": 44974}, {"_path": "Library/lib/glut.lib", "path_type": "hardlink", "sha256": "6e403161a3104893bd09ffa4a3d30527bf8da36c0db35521965721bf358f1f8f", "sha256_in_prefix": "6e403161a3104893bd09ffa4a3d30527bf8da36c0db35521965721bf358f1f8f", "size_in_bytes": 44974}, {"_path": "Library/lib/pkgconfig/glut.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "C:/b/abs_2et_101mpm/croot/freeglut_1715706044065/_h_env", "sha256": "dc1817ed859bf2708f26421af169ce77a5e7af3a79420bd933121d617491263a", "sha256_in_prefix": "aea8241b8ed115382b06ad79d02c00a0510557a5485a3d73d2ff377cd9e41a35", "size_in_bytes": 379}], "paths_version": 1}, "requested_spec": "None", "sha256": "9a078e53e7721c7af2834ee9c1a2fc22a6481ba99b138b5cad37537771d8ee48", "size": 136274, "subdir": "win-64", "timestamp": 1715706145000, "url": "https://repo.anaconda.com/pkgs/main/win-64/freeglut-3.4.0-hd77b12b_0.conda", "version": "3.4.0"}