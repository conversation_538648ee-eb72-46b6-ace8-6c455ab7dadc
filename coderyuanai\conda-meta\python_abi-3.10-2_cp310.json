{"build": "2_cp310", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["pypy <0a0"], "depends": ["python 3.10.*"], "extracted_package_dir": "C:\\Users\\<USER>\\.conda\\pkgs\\python_abi-3.10-2_cp310", "files": [], "fn": "python_abi-3.10-2_cp310.tar.bz2", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "C:\\Users\\<USER>\\.conda\\pkgs\\python_abi-3.10-2_cp310", "type": 1}, "md5": "aaa900b98edb2e67106b461ff365ba57", "name": "python_abi", "package_tarball_full_path": "C:\\Users\\<USER>\\.conda\\pkgs\\python_abi-3.10-2_cp310.tar.bz2", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "0871297da6dc3379aeddcb55787faf61aa44103b247b52aac381da3a1c794e45", "size": 4545, "subdir": "win-64", "timestamp": 1633504174000, "url": "https://conda.anaconda.org/conda-forge/win-64/python_abi-3.10-2_cp310.tar.bz2", "version": "3.10"}