{"build": "h57928b3_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": ["vs2015_runtime >=14.29.30037"], "depends": [], "extracted_package_dir": "C:\\Users\\<USER>\\.conda\\pkgs\\ucrt-10.0.22621.0-h57928b3_1", "files": ["Library/bin/api-ms-win-core-console-l1-1-0.dll", "Library/bin/api-ms-win-core-console-l1-2-0.dll", "Library/bin/api-ms-win-core-datetime-l1-1-0.dll", "Library/bin/api-ms-win-core-debug-l1-1-0.dll", "Library/bin/api-ms-win-core-errorhandling-l1-1-0.dll", "Library/bin/api-ms-win-core-fibers-l1-1-0.dll", "Library/bin/api-ms-win-core-file-l1-1-0.dll", "Library/bin/api-ms-win-core-file-l1-2-0.dll", "Library/bin/api-ms-win-core-file-l2-1-0.dll", "Library/bin/api-ms-win-core-handle-l1-1-0.dll", "Library/bin/api-ms-win-core-heap-l1-1-0.dll", "Library/bin/api-ms-win-core-interlocked-l1-1-0.dll", "Library/bin/api-ms-win-core-libraryloader-l1-1-0.dll", "Library/bin/api-ms-win-core-localization-l1-2-0.dll", "Library/bin/api-ms-win-core-memory-l1-1-0.dll", "Library/bin/api-ms-win-core-namedpipe-l1-1-0.dll", "Library/bin/api-ms-win-core-processenvironment-l1-1-0.dll", "Library/bin/api-ms-win-core-processthreads-l1-1-0.dll", "Library/bin/api-ms-win-core-processthreads-l1-1-1.dll", "Library/bin/api-ms-win-core-profile-l1-1-0.dll", "Library/bin/api-ms-win-core-rtlsupport-l1-1-0.dll", "Library/bin/api-ms-win-core-string-l1-1-0.dll", "Library/bin/api-ms-win-core-synch-l1-1-0.dll", "Library/bin/api-ms-win-core-synch-l1-2-0.dll", "Library/bin/api-ms-win-core-sysinfo-l1-1-0.dll", "Library/bin/api-ms-win-core-timezone-l1-1-0.dll", "Library/bin/api-ms-win-core-util-l1-1-0.dll", "Library/bin/api-ms-win-crt-conio-l1-1-0.dll", "Library/bin/api-ms-win-crt-convert-l1-1-0.dll", "Library/bin/api-ms-win-crt-environment-l1-1-0.dll", "Library/bin/api-ms-win-crt-filesystem-l1-1-0.dll", "Library/bin/api-ms-win-crt-heap-l1-1-0.dll", "Library/bin/api-ms-win-crt-locale-l1-1-0.dll", "Library/bin/api-ms-win-crt-math-l1-1-0.dll", "Library/bin/api-ms-win-crt-multibyte-l1-1-0.dll", "Library/bin/api-ms-win-crt-private-l1-1-0.dll", "Library/bin/api-ms-win-crt-process-l1-1-0.dll", "Library/bin/api-ms-win-crt-runtime-l1-1-0.dll", "Library/bin/api-ms-win-crt-stdio-l1-1-0.dll", "Library/bin/api-ms-win-crt-string-l1-1-0.dll", "Library/bin/api-ms-win-crt-time-l1-1-0.dll", "Library/bin/api-ms-win-crt-utility-l1-1-0.dll", "Library/bin/ucrtbase.dll", "api-ms-win-core-console-l1-1-0.dll", "api-ms-win-core-console-l1-2-0.dll", "api-ms-win-core-datetime-l1-1-0.dll", "api-ms-win-core-debug-l1-1-0.dll", "api-ms-win-core-errorhandling-l1-1-0.dll", "api-ms-win-core-fibers-l1-1-0.dll", "api-ms-win-core-file-l1-1-0.dll", "api-ms-win-core-file-l1-2-0.dll", "api-ms-win-core-file-l2-1-0.dll", "api-ms-win-core-handle-l1-1-0.dll", "api-ms-win-core-heap-l1-1-0.dll", "api-ms-win-core-interlocked-l1-1-0.dll", "api-ms-win-core-libraryloader-l1-1-0.dll", "api-ms-win-core-localization-l1-2-0.dll", "api-ms-win-core-memory-l1-1-0.dll", "api-ms-win-core-namedpipe-l1-1-0.dll", "api-ms-win-core-processenvironment-l1-1-0.dll", "api-ms-win-core-processthreads-l1-1-0.dll", "api-ms-win-core-processthreads-l1-1-1.dll", "api-ms-win-core-profile-l1-1-0.dll", "api-ms-win-core-rtlsupport-l1-1-0.dll", "api-ms-win-core-string-l1-1-0.dll", "api-ms-win-core-synch-l1-1-0.dll", "api-ms-win-core-synch-l1-2-0.dll", "api-ms-win-core-sysinfo-l1-1-0.dll", "api-ms-win-core-timezone-l1-1-0.dll", "api-ms-win-core-util-l1-1-0.dll", "api-ms-win-crt-conio-l1-1-0.dll", "api-ms-win-crt-convert-l1-1-0.dll", "api-ms-win-crt-environment-l1-1-0.dll", "api-ms-win-crt-filesystem-l1-1-0.dll", "api-ms-win-crt-heap-l1-1-0.dll", "api-ms-win-crt-locale-l1-1-0.dll", "api-ms-win-crt-math-l1-1-0.dll", "api-ms-win-crt-multibyte-l1-1-0.dll", "api-ms-win-crt-private-l1-1-0.dll", "api-ms-win-crt-process-l1-1-0.dll", "api-ms-win-crt-runtime-l1-1-0.dll", "api-ms-win-crt-stdio-l1-1-0.dll", "api-ms-win-crt-string-l1-1-0.dll", "api-ms-win-crt-time-l1-1-0.dll", "api-ms-win-crt-utility-l1-1-0.dll", "ucrtbase.dll"], "fn": "ucrt-10.0.22621.0-h57928b3_1.conda", "license": "LicenseRef-MicrosoftWindowsSDK10", "link": {"source": "C:\\Users\\<USER>\\.conda\\pkgs\\ucrt-10.0.22621.0-h57928b3_1", "type": 1}, "md5": "6797b005cd0f439c4c5c9ac565783700", "name": "ucrt", "package_tarball_full_path": "C:\\Users\\<USER>\\.conda\\pkgs\\ucrt-10.0.22621.0-h57928b3_1.conda", "paths_data": {"paths": [{"_path": "Library/bin/api-ms-win-core-console-l1-1-0.dll", "path_type": "hardlink", "sha256": "6df76986aec08e89ea037bc5271caaa82c69bd7eefba4fc293f92de1b1213e5e", "sha256_in_prefix": "6df76986aec08e89ea037bc5271caaa82c69bd7eefba4fc293f92de1b1213e5e", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-console-l1-2-0.dll", "path_type": "hardlink", "sha256": "05c7a25f72f40225ccf8af947a8e90580656228636d462a520b503de2c8e0aad", "sha256_in_prefix": "05c7a25f72f40225ccf8af947a8e90580656228636d462a520b503de2c8e0aad", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-datetime-l1-1-0.dll", "path_type": "hardlink", "sha256": "c541697c3e9086c6483e88d9cc8fe8a2efc74f663da0b0662babc04d3f79000f", "sha256_in_prefix": "c541697c3e9086c6483e88d9cc8fe8a2efc74f663da0b0662babc04d3f79000f", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-debug-l1-1-0.dll", "path_type": "hardlink", "sha256": "fed0c40d172de4de52dc2230c5c92fd6bc8e8553619d5bb5240dcc4f025dc8cf", "sha256_in_prefix": "fed0c40d172de4de52dc2230c5c92fd6bc8e8553619d5bb5240dcc4f025dc8cf", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-errorhandling-l1-1-0.dll", "path_type": "hardlink", "sha256": "cdae6e7d24d4ca4b491d483135740cc60f2dfc74dd2e01779e655b9f8f85a337", "sha256_in_prefix": "cdae6e7d24d4ca4b491d483135740cc60f2dfc74dd2e01779e655b9f8f85a337", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-fibers-l1-1-0.dll", "path_type": "hardlink", "sha256": "cbb6cb7ff25363b07647ea05d01f2881837d50b04bba4debe875a4aa3a307c52", "sha256_in_prefix": "cbb6cb7ff25363b07647ea05d01f2881837d50b04bba4debe875a4aa3a307c52", "size_in_bytes": 21992}, {"_path": "Library/bin/api-ms-win-core-file-l1-1-0.dll", "path_type": "hardlink", "sha256": "02c96dde747ea574fecfb93d43dd9829236a1b22f0eb5513c0e0b27b7bdac934", "sha256_in_prefix": "02c96dde747ea574fecfb93d43dd9829236a1b22f0eb5513c0e0b27b7bdac934", "size_in_bytes": 26088}, {"_path": "Library/bin/api-ms-win-core-file-l1-2-0.dll", "path_type": "hardlink", "sha256": "682968c7640a1d55485b7c10dfdaf520b1e1c73a8be267853e80be893be3fb49", "sha256_in_prefix": "682968c7640a1d55485b7c10dfdaf520b1e1c73a8be267853e80be893be3fb49", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-file-l2-1-0.dll", "path_type": "hardlink", "sha256": "45a0eb1b83f448054536d3aa628393b7418477897e841c66384ecc7f4f18c2f2", "sha256_in_prefix": "45a0eb1b83f448054536d3aa628393b7418477897e841c66384ecc7f4f18c2f2", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-handle-l1-1-0.dll", "path_type": "hardlink", "sha256": "9986a39b7f2e067ee7d1f2b3db0b940e2b75900152f099ff41cce8ddd47565e0", "sha256_in_prefix": "9986a39b7f2e067ee7d1f2b3db0b940e2b75900152f099ff41cce8ddd47565e0", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "54b51444c8b6c9342fd20f5a5b0909a906acd0bdc9cf4d70134d5f8cffbe3209", "sha256_in_prefix": "54b51444c8b6c9342fd20f5a5b0909a906acd0bdc9cf4d70134d5f8cffbe3209", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-interlocked-l1-1-0.dll", "path_type": "hardlink", "sha256": "ff341ef2c2c389d44bbb1c919ff42ec360015985c0dae2ac760ac5acad0cb1d1", "sha256_in_prefix": "ff341ef2c2c389d44bbb1c919ff42ec360015985c0dae2ac760ac5acad0cb1d1", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-libraryloader-l1-1-0.dll", "path_type": "hardlink", "sha256": "6cbc929f814d7e931e0f6f510da1696b059c53bf66934a68d218d3342ce4a289", "sha256_in_prefix": "6cbc929f814d7e931e0f6f510da1696b059c53bf66934a68d218d3342ce4a289", "size_in_bytes": 22008}, {"_path": "Library/bin/api-ms-win-core-localization-l1-2-0.dll", "path_type": "hardlink", "sha256": "3ddbcd067d495845b7134f30bcea031ad558df4acb562b2f3190941913227158", "sha256_in_prefix": "3ddbcd067d495845b7134f30bcea031ad558df4acb562b2f3190941913227158", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-memory-l1-1-0.dll", "path_type": "hardlink", "sha256": "bfa2145c3d615540193a41c9f9605018062307e9fcf1665c390f0034178ecb4a", "sha256_in_prefix": "bfa2145c3d615540193a41c9f9605018062307e9fcf1665c390f0034178ecb4a", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-namedpipe-l1-1-0.dll", "path_type": "hardlink", "sha256": "4fcb066de08f48a490e7d50417469ab3099b4a7fe318a8845e4ec9b3e3fd52be", "sha256_in_prefix": "4fcb066de08f48a490e7d50417469ab3099b4a7fe318a8845e4ec9b3e3fd52be", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-processenvironment-l1-1-0.dll", "path_type": "hardlink", "sha256": "f3a31997c715dec8a6857d87be217c7acb893839156838ba20b5ef818ad5c9b1", "sha256_in_prefix": "f3a31997c715dec8a6857d87be217c7acb893839156838ba20b5ef818ad5c9b1", "size_in_bytes": 22008}, {"_path": "Library/bin/api-ms-win-core-processthreads-l1-1-0.dll", "path_type": "hardlink", "sha256": "470e9d04a1aa19c6b84b5b10cb055ff55caabe0f45d63090865c196b66c295f5", "sha256_in_prefix": "470e9d04a1aa19c6b84b5b10cb055ff55caabe0f45d63090865c196b66c295f5", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-processthreads-l1-1-1.dll", "path_type": "hardlink", "sha256": "4e57ceffbc2ef0e4dbfb92854756f97d2b34d9e29db16f46c476ed360b92b1cc", "sha256_in_prefix": "4e57ceffbc2ef0e4dbfb92854756f97d2b34d9e29db16f46c476ed360b92b1cc", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-profile-l1-1-0.dll", "path_type": "hardlink", "sha256": "0a3a017ca780353d56157682edda4cc3a19a236625fe0f2356faf635cd4f6ace", "sha256_in_prefix": "0a3a017ca780353d56157682edda4cc3a19a236625fe0f2356faf635cd4f6ace", "size_in_bytes": 21992}, {"_path": "Library/bin/api-ms-win-core-rtlsupport-l1-1-0.dll", "path_type": "hardlink", "sha256": "243c00a13c8fd6d764ebf22ec5a93492043949616561697b776fe9f62360665b", "sha256_in_prefix": "243c00a13c8fd6d764ebf22ec5a93492043949616561697b776fe9f62360665b", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "be439f71ed591f8f65b8894e84569cf3cc3363d88536e49bdd998e49e069d0e8", "sha256_in_prefix": "be439f71ed591f8f65b8894e84569cf3cc3363d88536e49bdd998e49e069d0e8", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-synch-l1-1-0.dll", "path_type": "hardlink", "sha256": "83e86bee356668319f4115846ed9a571cdb37cd8a0eb036fc2a960bdeafcb526", "sha256_in_prefix": "83e86bee356668319f4115846ed9a571cdb37cd8a0eb036fc2a960bdeafcb526", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-synch-l1-2-0.dll", "path_type": "hardlink", "sha256": "718bbb66ed24612c40f415ef11f07db287b1bdf0b130667689d1cdce3a1bee29", "sha256_in_prefix": "718bbb66ed24612c40f415ef11f07db287b1bdf0b130667689d1cdce3a1bee29", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-sysinfo-l1-1-0.dll", "path_type": "hardlink", "sha256": "8e28db596d46ca22f16d8825bebc9406cd01d5efe4233a2eaa6b450473741766", "sha256_in_prefix": "8e28db596d46ca22f16d8825bebc9406cd01d5efe4233a2eaa6b450473741766", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-core-timezone-l1-1-0.dll", "path_type": "hardlink", "sha256": "b29f3f250eed6d05bc000a1020ca65215838c3733b78293dfca459031df575f8", "sha256_in_prefix": "b29f3f250eed6d05bc000a1020ca65215838c3733b78293dfca459031df575f8", "size_in_bytes": 21976}, {"_path": "Library/bin/api-ms-win-core-util-l1-1-0.dll", "path_type": "hardlink", "sha256": "4f79b6338f8438e63627b174d5b1bee2d2dcfc40a6119221317fe3f0d8b1e1a0", "sha256_in_prefix": "4f79b6338f8438e63627b174d5b1bee2d2dcfc40a6119221317fe3f0d8b1e1a0", "size_in_bytes": 21992}, {"_path": "Library/bin/api-ms-win-crt-conio-l1-1-0.dll", "path_type": "hardlink", "sha256": "7320687be8b742cd70ac0e5f1b0a854502063f9b291147785aa7355a38de162d", "sha256_in_prefix": "7320687be8b742cd70ac0e5f1b0a854502063f9b291147785aa7355a38de162d", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-convert-l1-1-0.dll", "path_type": "hardlink", "sha256": "3550d96592baf95f0be865503d98f47c8c8d4d36b01190589bb7bd08585c739d", "sha256_in_prefix": "3550d96592baf95f0be865503d98f47c8c8d4d36b01190589bb7bd08585c739d", "size_in_bytes": 26080}, {"_path": "Library/bin/api-ms-win-crt-environment-l1-1-0.dll", "path_type": "hardlink", "sha256": "167a2b52964458b16aaa166de281e52c35dfe920380c9c7783a06678b665b2ba", "sha256_in_prefix": "167a2b52964458b16aaa166de281e52c35dfe920380c9c7783a06678b665b2ba", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-filesystem-l1-1-0.dll", "path_type": "hardlink", "sha256": "255c9a44ed1106c673821ed4ef165788d6bcc94b2924d0fa1d36af4fc2eb0b9c", "sha256_in_prefix": "255c9a44ed1106c673821ed4ef165788d6bcc94b2924d0fa1d36af4fc2eb0b9c", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "dd4be34d6de584a82f12803b1be98afd48bf2f82e87b8cb77141b41ea6393cd5", "sha256_in_prefix": "dd4be34d6de584a82f12803b1be98afd48bf2f82e87b8cb77141b41ea6393cd5", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-locale-l1-1-0.dll", "path_type": "hardlink", "sha256": "93fe93380fae70f12ebe85b92e5326321400861167ee0ac858a81d2f7fa37d4b", "sha256_in_prefix": "93fe93380fae70f12ebe85b92e5326321400861167ee0ac858a81d2f7fa37d4b", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-math-l1-1-0.dll", "path_type": "hardlink", "sha256": "6ea15153e8948e7fc9e0aee7cfc5cb7b9b8f872f94e6714bc510b9bc7291ad9b", "sha256_in_prefix": "6ea15153e8948e7fc9e0aee7cfc5cb7b9b8f872f94e6714bc510b9bc7291ad9b", "size_in_bytes": 30184}, {"_path": "Library/bin/api-ms-win-crt-multibyte-l1-1-0.dll", "path_type": "hardlink", "sha256": "edfb4fcd63eef1adebf52c0fc7e6029883201c97be4d9bed9bf203d0ff595301", "sha256_in_prefix": "edfb4fcd63eef1adebf52c0fc7e6029883201c97be4d9bed9bf203d0ff595301", "size_in_bytes": 30176}, {"_path": "Library/bin/api-ms-win-crt-private-l1-1-0.dll", "path_type": "hardlink", "sha256": "558ce3eb8b6fe5752008385e225df7a74eae6e8fef74ff0b23ee4ee7e21f87dd", "sha256_in_prefix": "558ce3eb8b6fe5752008385e225df7a74eae6e8fef74ff0b23ee4ee7e21f87dd", "size_in_bytes": 75232}, {"_path": "Library/bin/api-ms-win-crt-process-l1-1-0.dll", "path_type": "hardlink", "sha256": "d92c5cd5e4e3738ac13e28e55de12e6da1ffda6f8e1c7687e0a0b4092c6b3462", "sha256_in_prefix": "d92c5cd5e4e3738ac13e28e55de12e6da1ffda6f8e1c7687e0a0b4092c6b3462", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-runtime-l1-1-0.dll", "path_type": "hardlink", "sha256": "1aec90818ef928b5b6ca252e2ac3ffe4ecc1f169251eba720d0035dcb22ba322", "sha256_in_prefix": "1aec90818ef928b5b6ca252e2ac3ffe4ecc1f169251eba720d0035dcb22ba322", "size_in_bytes": 26104}, {"_path": "Library/bin/api-ms-win-crt-stdio-l1-1-0.dll", "path_type": "hardlink", "sha256": "13328be331666a312d58b6a1b537f8b9ac8922de02e22ddcd0ec84a550e59715", "sha256_in_prefix": "13328be331666a312d58b6a1b537f8b9ac8922de02e22ddcd0ec84a550e59715", "size_in_bytes": 26080}, {"_path": "Library/bin/api-ms-win-crt-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "e45dd01a9f6edcf992f3c74c0eb41178851490db9123a4c738497fbf4f73ec0e", "sha256_in_prefix": "e45dd01a9f6edcf992f3c74c0eb41178851490db9123a4c738497fbf4f73ec0e", "size_in_bytes": 26072}, {"_path": "Library/bin/api-ms-win-crt-time-l1-1-0.dll", "path_type": "hardlink", "sha256": "0cf285ef569249aaa3fb95de1793fc01895f6805ada65d801302f430b62a5153", "sha256_in_prefix": "0cf285ef569249aaa3fb95de1793fc01895f6805ada65d801302f430b62a5153", "size_in_bytes": 21984}, {"_path": "Library/bin/api-ms-win-crt-utility-l1-1-0.dll", "path_type": "hardlink", "sha256": "d2a27ff370b4f5c87e5475573d23ee261825a9cd1dd55ee0551a361f61414209", "sha256_in_prefix": "d2a27ff370b4f5c87e5475573d23ee261825a9cd1dd55ee0551a361f61414209", "size_in_bytes": 22000}, {"_path": "Library/bin/ucrtbase.dll", "path_type": "hardlink", "sha256": "fbed69a52fdcf571dd37fe4cc63cb86ed3732b5b998807f14968788027c00754", "sha256_in_prefix": "fbed69a52fdcf571dd37fe4cc63cb86ed3732b5b998807f14968788027c00754", "size_in_bytes": 1123808}, {"_path": "api-ms-win-core-console-l1-1-0.dll", "path_type": "hardlink", "sha256": "6df76986aec08e89ea037bc5271caaa82c69bd7eefba4fc293f92de1b1213e5e", "sha256_in_prefix": "6df76986aec08e89ea037bc5271caaa82c69bd7eefba4fc293f92de1b1213e5e", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-console-l1-2-0.dll", "path_type": "hardlink", "sha256": "05c7a25f72f40225ccf8af947a8e90580656228636d462a520b503de2c8e0aad", "sha256_in_prefix": "05c7a25f72f40225ccf8af947a8e90580656228636d462a520b503de2c8e0aad", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-datetime-l1-1-0.dll", "path_type": "hardlink", "sha256": "c541697c3e9086c6483e88d9cc8fe8a2efc74f663da0b0662babc04d3f79000f", "sha256_in_prefix": "c541697c3e9086c6483e88d9cc8fe8a2efc74f663da0b0662babc04d3f79000f", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-debug-l1-1-0.dll", "path_type": "hardlink", "sha256": "fed0c40d172de4de52dc2230c5c92fd6bc8e8553619d5bb5240dcc4f025dc8cf", "sha256_in_prefix": "fed0c40d172de4de52dc2230c5c92fd6bc8e8553619d5bb5240dcc4f025dc8cf", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-errorhandling-l1-1-0.dll", "path_type": "hardlink", "sha256": "cdae6e7d24d4ca4b491d483135740cc60f2dfc74dd2e01779e655b9f8f85a337", "sha256_in_prefix": "cdae6e7d24d4ca4b491d483135740cc60f2dfc74dd2e01779e655b9f8f85a337", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-fibers-l1-1-0.dll", "path_type": "hardlink", "sha256": "cbb6cb7ff25363b07647ea05d01f2881837d50b04bba4debe875a4aa3a307c52", "sha256_in_prefix": "cbb6cb7ff25363b07647ea05d01f2881837d50b04bba4debe875a4aa3a307c52", "size_in_bytes": 21992}, {"_path": "api-ms-win-core-file-l1-1-0.dll", "path_type": "hardlink", "sha256": "02c96dde747ea574fecfb93d43dd9829236a1b22f0eb5513c0e0b27b7bdac934", "sha256_in_prefix": "02c96dde747ea574fecfb93d43dd9829236a1b22f0eb5513c0e0b27b7bdac934", "size_in_bytes": 26088}, {"_path": "api-ms-win-core-file-l1-2-0.dll", "path_type": "hardlink", "sha256": "682968c7640a1d55485b7c10dfdaf520b1e1c73a8be267853e80be893be3fb49", "sha256_in_prefix": "682968c7640a1d55485b7c10dfdaf520b1e1c73a8be267853e80be893be3fb49", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-file-l2-1-0.dll", "path_type": "hardlink", "sha256": "45a0eb1b83f448054536d3aa628393b7418477897e841c66384ecc7f4f18c2f2", "sha256_in_prefix": "45a0eb1b83f448054536d3aa628393b7418477897e841c66384ecc7f4f18c2f2", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-handle-l1-1-0.dll", "path_type": "hardlink", "sha256": "9986a39b7f2e067ee7d1f2b3db0b940e2b75900152f099ff41cce8ddd47565e0", "sha256_in_prefix": "9986a39b7f2e067ee7d1f2b3db0b940e2b75900152f099ff41cce8ddd47565e0", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "54b51444c8b6c9342fd20f5a5b0909a906acd0bdc9cf4d70134d5f8cffbe3209", "sha256_in_prefix": "54b51444c8b6c9342fd20f5a5b0909a906acd0bdc9cf4d70134d5f8cffbe3209", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-interlocked-l1-1-0.dll", "path_type": "hardlink", "sha256": "ff341ef2c2c389d44bbb1c919ff42ec360015985c0dae2ac760ac5acad0cb1d1", "sha256_in_prefix": "ff341ef2c2c389d44bbb1c919ff42ec360015985c0dae2ac760ac5acad0cb1d1", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-libraryloader-l1-1-0.dll", "path_type": "hardlink", "sha256": "6cbc929f814d7e931e0f6f510da1696b059c53bf66934a68d218d3342ce4a289", "sha256_in_prefix": "6cbc929f814d7e931e0f6f510da1696b059c53bf66934a68d218d3342ce4a289", "size_in_bytes": 22008}, {"_path": "api-ms-win-core-localization-l1-2-0.dll", "path_type": "hardlink", "sha256": "3ddbcd067d495845b7134f30bcea031ad558df4acb562b2f3190941913227158", "sha256_in_prefix": "3ddbcd067d495845b7134f30bcea031ad558df4acb562b2f3190941913227158", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-memory-l1-1-0.dll", "path_type": "hardlink", "sha256": "bfa2145c3d615540193a41c9f9605018062307e9fcf1665c390f0034178ecb4a", "sha256_in_prefix": "bfa2145c3d615540193a41c9f9605018062307e9fcf1665c390f0034178ecb4a", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-namedpipe-l1-1-0.dll", "path_type": "hardlink", "sha256": "4fcb066de08f48a490e7d50417469ab3099b4a7fe318a8845e4ec9b3e3fd52be", "sha256_in_prefix": "4fcb066de08f48a490e7d50417469ab3099b4a7fe318a8845e4ec9b3e3fd52be", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-processenvironment-l1-1-0.dll", "path_type": "hardlink", "sha256": "f3a31997c715dec8a6857d87be217c7acb893839156838ba20b5ef818ad5c9b1", "sha256_in_prefix": "f3a31997c715dec8a6857d87be217c7acb893839156838ba20b5ef818ad5c9b1", "size_in_bytes": 22008}, {"_path": "api-ms-win-core-processthreads-l1-1-0.dll", "path_type": "hardlink", "sha256": "470e9d04a1aa19c6b84b5b10cb055ff55caabe0f45d63090865c196b66c295f5", "sha256_in_prefix": "470e9d04a1aa19c6b84b5b10cb055ff55caabe0f45d63090865c196b66c295f5", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-processthreads-l1-1-1.dll", "path_type": "hardlink", "sha256": "4e57ceffbc2ef0e4dbfb92854756f97d2b34d9e29db16f46c476ed360b92b1cc", "sha256_in_prefix": "4e57ceffbc2ef0e4dbfb92854756f97d2b34d9e29db16f46c476ed360b92b1cc", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-profile-l1-1-0.dll", "path_type": "hardlink", "sha256": "0a3a017ca780353d56157682edda4cc3a19a236625fe0f2356faf635cd4f6ace", "sha256_in_prefix": "0a3a017ca780353d56157682edda4cc3a19a236625fe0f2356faf635cd4f6ace", "size_in_bytes": 21992}, {"_path": "api-ms-win-core-rtlsupport-l1-1-0.dll", "path_type": "hardlink", "sha256": "243c00a13c8fd6d764ebf22ec5a93492043949616561697b776fe9f62360665b", "sha256_in_prefix": "243c00a13c8fd6d764ebf22ec5a93492043949616561697b776fe9f62360665b", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "be439f71ed591f8f65b8894e84569cf3cc3363d88536e49bdd998e49e069d0e8", "sha256_in_prefix": "be439f71ed591f8f65b8894e84569cf3cc3363d88536e49bdd998e49e069d0e8", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-synch-l1-1-0.dll", "path_type": "hardlink", "sha256": "83e86bee356668319f4115846ed9a571cdb37cd8a0eb036fc2a960bdeafcb526", "sha256_in_prefix": "83e86bee356668319f4115846ed9a571cdb37cd8a0eb036fc2a960bdeafcb526", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-synch-l1-2-0.dll", "path_type": "hardlink", "sha256": "718bbb66ed24612c40f415ef11f07db287b1bdf0b130667689d1cdce3a1bee29", "sha256_in_prefix": "718bbb66ed24612c40f415ef11f07db287b1bdf0b130667689d1cdce3a1bee29", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-sysinfo-l1-1-0.dll", "path_type": "hardlink", "sha256": "8e28db596d46ca22f16d8825bebc9406cd01d5efe4233a2eaa6b450473741766", "sha256_in_prefix": "8e28db596d46ca22f16d8825bebc9406cd01d5efe4233a2eaa6b450473741766", "size_in_bytes": 21984}, {"_path": "api-ms-win-core-timezone-l1-1-0.dll", "path_type": "hardlink", "sha256": "b29f3f250eed6d05bc000a1020ca65215838c3733b78293dfca459031df575f8", "sha256_in_prefix": "b29f3f250eed6d05bc000a1020ca65215838c3733b78293dfca459031df575f8", "size_in_bytes": 21976}, {"_path": "api-ms-win-core-util-l1-1-0.dll", "path_type": "hardlink", "sha256": "4f79b6338f8438e63627b174d5b1bee2d2dcfc40a6119221317fe3f0d8b1e1a0", "sha256_in_prefix": "4f79b6338f8438e63627b174d5b1bee2d2dcfc40a6119221317fe3f0d8b1e1a0", "size_in_bytes": 21992}, {"_path": "api-ms-win-crt-conio-l1-1-0.dll", "path_type": "hardlink", "sha256": "7320687be8b742cd70ac0e5f1b0a854502063f9b291147785aa7355a38de162d", "sha256_in_prefix": "7320687be8b742cd70ac0e5f1b0a854502063f9b291147785aa7355a38de162d", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-convert-l1-1-0.dll", "path_type": "hardlink", "sha256": "3550d96592baf95f0be865503d98f47c8c8d4d36b01190589bb7bd08585c739d", "sha256_in_prefix": "3550d96592baf95f0be865503d98f47c8c8d4d36b01190589bb7bd08585c739d", "size_in_bytes": 26080}, {"_path": "api-ms-win-crt-environment-l1-1-0.dll", "path_type": "hardlink", "sha256": "167a2b52964458b16aaa166de281e52c35dfe920380c9c7783a06678b665b2ba", "sha256_in_prefix": "167a2b52964458b16aaa166de281e52c35dfe920380c9c7783a06678b665b2ba", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-filesystem-l1-1-0.dll", "path_type": "hardlink", "sha256": "255c9a44ed1106c673821ed4ef165788d6bcc94b2924d0fa1d36af4fc2eb0b9c", "sha256_in_prefix": "255c9a44ed1106c673821ed4ef165788d6bcc94b2924d0fa1d36af4fc2eb0b9c", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-heap-l1-1-0.dll", "path_type": "hardlink", "sha256": "dd4be34d6de584a82f12803b1be98afd48bf2f82e87b8cb77141b41ea6393cd5", "sha256_in_prefix": "dd4be34d6de584a82f12803b1be98afd48bf2f82e87b8cb77141b41ea6393cd5", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-locale-l1-1-0.dll", "path_type": "hardlink", "sha256": "93fe93380fae70f12ebe85b92e5326321400861167ee0ac858a81d2f7fa37d4b", "sha256_in_prefix": "93fe93380fae70f12ebe85b92e5326321400861167ee0ac858a81d2f7fa37d4b", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-math-l1-1-0.dll", "path_type": "hardlink", "sha256": "6ea15153e8948e7fc9e0aee7cfc5cb7b9b8f872f94e6714bc510b9bc7291ad9b", "sha256_in_prefix": "6ea15153e8948e7fc9e0aee7cfc5cb7b9b8f872f94e6714bc510b9bc7291ad9b", "size_in_bytes": 30184}, {"_path": "api-ms-win-crt-multibyte-l1-1-0.dll", "path_type": "hardlink", "sha256": "edfb4fcd63eef1adebf52c0fc7e6029883201c97be4d9bed9bf203d0ff595301", "sha256_in_prefix": "edfb4fcd63eef1adebf52c0fc7e6029883201c97be4d9bed9bf203d0ff595301", "size_in_bytes": 30176}, {"_path": "api-ms-win-crt-private-l1-1-0.dll", "path_type": "hardlink", "sha256": "558ce3eb8b6fe5752008385e225df7a74eae6e8fef74ff0b23ee4ee7e21f87dd", "sha256_in_prefix": "558ce3eb8b6fe5752008385e225df7a74eae6e8fef74ff0b23ee4ee7e21f87dd", "size_in_bytes": 75232}, {"_path": "api-ms-win-crt-process-l1-1-0.dll", "path_type": "hardlink", "sha256": "d92c5cd5e4e3738ac13e28e55de12e6da1ffda6f8e1c7687e0a0b4092c6b3462", "sha256_in_prefix": "d92c5cd5e4e3738ac13e28e55de12e6da1ffda6f8e1c7687e0a0b4092c6b3462", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-runtime-l1-1-0.dll", "path_type": "hardlink", "sha256": "1aec90818ef928b5b6ca252e2ac3ffe4ecc1f169251eba720d0035dcb22ba322", "sha256_in_prefix": "1aec90818ef928b5b6ca252e2ac3ffe4ecc1f169251eba720d0035dcb22ba322", "size_in_bytes": 26104}, {"_path": "api-ms-win-crt-stdio-l1-1-0.dll", "path_type": "hardlink", "sha256": "13328be331666a312d58b6a1b537f8b9ac8922de02e22ddcd0ec84a550e59715", "sha256_in_prefix": "13328be331666a312d58b6a1b537f8b9ac8922de02e22ddcd0ec84a550e59715", "size_in_bytes": 26080}, {"_path": "api-ms-win-crt-string-l1-1-0.dll", "path_type": "hardlink", "sha256": "e45dd01a9f6edcf992f3c74c0eb41178851490db9123a4c738497fbf4f73ec0e", "sha256_in_prefix": "e45dd01a9f6edcf992f3c74c0eb41178851490db9123a4c738497fbf4f73ec0e", "size_in_bytes": 26072}, {"_path": "api-ms-win-crt-time-l1-1-0.dll", "path_type": "hardlink", "sha256": "0cf285ef569249aaa3fb95de1793fc01895f6805ada65d801302f430b62a5153", "sha256_in_prefix": "0cf285ef569249aaa3fb95de1793fc01895f6805ada65d801302f430b62a5153", "size_in_bytes": 21984}, {"_path": "api-ms-win-crt-utility-l1-1-0.dll", "path_type": "hardlink", "sha256": "d2a27ff370b4f5c87e5475573d23ee261825a9cd1dd55ee0551a361f61414209", "sha256_in_prefix": "d2a27ff370b4f5c87e5475573d23ee261825a9cd1dd55ee0551a361f61414209", "size_in_bytes": 22000}, {"_path": "ucrtbase.dll", "path_type": "hardlink", "sha256": "fbed69a52fdcf571dd37fe4cc63cb86ed3732b5b998807f14968788027c00754", "sha256_in_prefix": "fbed69a52fdcf571dd37fe4cc63cb86ed3732b5b998807f14968788027c00754", "size_in_bytes": 1123808}], "paths_version": 1}, "requested_spec": "None", "sha256": "db8dead3dd30fb1a032737554ce91e2819b43496a0db09927edf01c32b577450", "size": 559710, "subdir": "win-64", "timestamp": 1728377334000, "url": "https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.22621.0-h57928b3_1.conda", "version": "10.0.22621.0"}