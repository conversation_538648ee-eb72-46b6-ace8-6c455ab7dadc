{"build": "py310h232114e_6", "build_number": 6, "channel": "https://conda.anaconda.org/conda-forge/win-64", "constrains": [], "depends": ["graphviz", "ucrt >=10.0.20348.0", "vc >=14.2,<15", "vc14_runtime >=14.29.30139", "python_abi 3.10.* *_cp310", "python >=3.10,<3.11.0a0", "dlfcn-win32 >=1.3.0,<2.0a0", "openfst >=1.8.2,<1.8.3.0a0", "openfst 1.8.2"], "extracted_package_dir": "C:\\Users\\<USER>\\.conda\\pkgs\\pynini-2.1.5-py310h232114e_6", "files": ["Lib/site-packages/_pynini.cp310-win_amd64.pyd", "Lib/site-packages/_pywrapfst.cp310-win_amd64.pyd", "Lib/site-packages/pynini-2.1.5.dist-info/AUTHORS", "Lib/site-packages/pynini-2.1.5.dist-info/INSTALLER", "Lib/site-packages/pynini-2.1.5.dist-info/LICENSE", "Lib/site-packages/pynini-2.1.5.dist-info/METADATA", "Lib/site-packages/pynini-2.1.5.dist-info/RECORD", "Lib/site-packages/pynini-2.1.5.dist-info/REQUESTED", "Lib/site-packages/pynini-2.1.5.dist-info/WHEEL", "Lib/site-packages/pynini-2.1.5.dist-info/direct_url.json", "Lib/site-packages/pynini-2.1.5.dist-info/top_level.txt", "Lib/site-packages/pynini/__init__.py", "Lib/site-packages/pynini/__init__.pyi", "Lib/site-packages/pynini/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pynini/examples/__init__.py", "Lib/site-packages/pynini/examples/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/case.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/chatspeak.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/chatspeak_model.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/dates.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/g2p.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/numbers.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/plurals.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/t9.cpython-310.pyc", "Lib/site-packages/pynini/examples/__pycache__/weather.cpython-310.pyc", "Lib/site-packages/pynini/examples/case.py", "Lib/site-packages/pynini/examples/chatspeak.py", "Lib/site-packages/pynini/examples/chatspeak_model.py", "Lib/site-packages/pynini/examples/dates.py", "Lib/site-packages/pynini/examples/g2p.py", "Lib/site-packages/pynini/examples/numbers.py", "Lib/site-packages/pynini/examples/plurals.py", "Lib/site-packages/pynini/examples/py.typed", "Lib/site-packages/pynini/examples/t9.py", "Lib/site-packages/pynini/examples/weather.py", "Lib/site-packages/pynini/export/__init__.py", "Lib/site-packages/pynini/export/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pynini/export/__pycache__/export.cpython-310.pyc", "Lib/site-packages/pynini/export/__pycache__/grm.cpython-310.pyc", "Lib/site-packages/pynini/export/__pycache__/grm_example.cpython-310.pyc", "Lib/site-packages/pynini/export/__pycache__/multi_grm.cpython-310.pyc", "Lib/site-packages/pynini/export/__pycache__/multi_grm_example.cpython-310.pyc", "Lib/site-packages/pynini/export/export.py", "Lib/site-packages/pynini/export/grm.py", "Lib/site-packages/pynini/export/grm_example.py", "Lib/site-packages/pynini/export/multi_grm.py", "Lib/site-packages/pynini/export/multi_grm_example.py", "Lib/site-packages/pynini/export/py.typed", "Lib/site-packages/pynini/lib/__init__.py", "Lib/site-packages/pynini/lib/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/byte.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/edit_transducer.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/features.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/paradigms.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/pynutil.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/rewrite.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/rule_cascade.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/tagger.cpython-310.pyc", "Lib/site-packages/pynini/lib/__pycache__/utf8.cpython-310.pyc", "Lib/site-packages/pynini/lib/byte.py", "Lib/site-packages/pynini/lib/edit_transducer.py", "Lib/site-packages/pynini/lib/features.py", "Lib/site-packages/pynini/lib/paradigms.py", "Lib/site-packages/pynini/lib/py.typed", "Lib/site-packages/pynini/lib/pynutil.py", "Lib/site-packages/pynini/lib/rewrite.py", "Lib/site-packages/pynini/lib/rule_cascade.py", "Lib/site-packages/pynini/lib/tagger.py", "Lib/site-packages/pynini/lib/utf8.py", "Lib/site-packages/pynini/py.typed", "Lib/site-packages/pywrapfst/__init__.py", "Lib/site-packages/pywrapfst/__init__.pyi", "Lib/site-packages/pywrapfst/__pycache__/__init__.cpython-310.pyc", "Lib/site-packages/pywrapfst/py.typed", "Library/include/_pywrapfst.h"], "fn": "pynini-2.1.5-py310h232114e_6.conda", "license": "Apache-2.0", "link": {"source": "C:\\Users\\<USER>\\.conda\\pkgs\\pynini-2.1.5-py310h232114e_6", "type": 1}, "md5": "56daf771ec141aec532e214db32636a2", "name": "p<PERSON><PERSON>", "package_tarball_full_path": "C:\\Users\\<USER>\\.conda\\pkgs\\pynini-2.1.5-py310h232114e_6.conda", "paths_data": {"paths": [{"_path": "Lib/site-packages/_pynini.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "dc3492cdb932dc37476d08d752697a8818bc50b8c840466f31078c339309e18d", "sha256_in_prefix": "dc3492cdb932dc37476d08d752697a8818bc50b8c840466f31078c339309e18d", "size_in_bytes": 2620928}, {"_path": "Lib/site-packages/_pywrapfst.cp310-win_amd64.pyd", "path_type": "hardlink", "sha256": "6534ff91f186c187c0d77dfe278f4e5b1ee36de41900d487f124cddc932987b2", "sha256_in_prefix": "6534ff91f186c187c0d77dfe278f4e5b1ee36de41900d487f124cddc932987b2", "size_in_bytes": 582144}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/AUTHORS", "path_type": "hardlink", "sha256": "627c695f80fdac6412d66e6ad4160a4acb41923ca7ee3fd7e112e105f0ff46b9", "sha256_in_prefix": "627c695f80fdac6412d66e6ad4160a4acb41923ca7ee3fd7e112e105f0ff46b9", "size_in_bytes": 12}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/METADATA", "path_type": "hardlink", "sha256": "34bf7d50a41df259e6a4dfd81613bdc8dee1441746ee5dba4bad673a44da45fe", "sha256_in_prefix": "34bf7d50a41df259e6a4dfd81613bdc8dee1441746ee5dba4bad673a44da45fe", "size_in_bytes": 4699}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/RECORD", "path_type": "hardlink", "sha256": "4224e94f2cd3813d8b493825214560ad0bc82eaf8543452d501cc8a56372b0ff", "sha256_in_prefix": "4224e94f2cd3813d8b493825214560ad0bc82eaf8543452d501cc8a56372b0ff", "size_in_bytes": 5219}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/WHEEL", "path_type": "hardlink", "sha256": "cabbed795019cf142fb439f3742461e1d3f4d6c3c8c5884b5c85e9942ee8e741", "sha256_in_prefix": "cabbed795019cf142fb439f3742461e1d3f4d6c3c8c5884b5c85e9942ee8e741", "size_in_bytes": 102}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "76e800e600ceae904a9706c21be202c1f4798892007a5585eafa19d6ee2127bf", "sha256_in_prefix": "76e800e600ceae904a9706c21be202c1f4798892007a5585eafa19d6ee2127bf", "size_in_bytes": 67}, {"_path": "Lib/site-packages/pynini-2.1.5.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "5f4da728fd3603821bc65e4b634137314d2387851b5713c3e9978a9c6404fbdf", "sha256_in_prefix": "5f4da728fd3603821bc65e4b634137314d2387851b5713c3e9978a9c6404fbdf", "size_in_bytes": 36}, {"_path": "Lib/site-packages/pynini/__init__.py", "path_type": "hardlink", "sha256": "503e55c387d30923c8dacfb9fa381b5c8184d9c1be3e73f113e72cd210b7863d", "sha256_in_prefix": "503e55c387d30923c8dacfb9fa381b5c8184d9c1be3e73f113e72cd210b7863d", "size_in_bytes": 79}, {"_path": "Lib/site-packages/pynini/__init__.pyi", "path_type": "hardlink", "sha256": "ff0fe8611706106ebb6cc5ab1bd16c7db7d6eab089b8de8144aacf41ceda19fb", "sha256_in_prefix": "ff0fe8611706106ebb6cc5ab1bd16c7db7d6eab089b8de8144aacf41ceda19fb", "size_in_bytes": 19348}, {"_path": "Lib/site-packages/pynini/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "4c592caa0306508885996be881b105b73b2e535084009be2b656a2f7a0ff6e07", "sha256_in_prefix": "4c592caa0306508885996be881b105b73b2e535084009be2b656a2f7a0ff6e07", "size_in_bytes": 179}, {"_path": "Lib/site-packages/pynini/examples/__init__.py", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "70e9e3e6d7790290c8d95630d0dd41cfee1666120c322951ed970b4e11f9369b", "sha256_in_prefix": "70e9e3e6d7790290c8d95630d0dd41cfee1666120c322951ed970b4e11f9369b", "size_in_bytes": 138}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/case.cpython-310.pyc", "path_type": "hardlink", "sha256": "703ef4bff3fb0fe6feb932c2e377f5c867be1005cc8f17e808166a55b80cc4f8", "sha256_in_prefix": "703ef4bff3fb0fe6feb932c2e377f5c867be1005cc8f17e808166a55b80cc4f8", "size_in_bytes": 2328}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/chatspeak.cpython-310.pyc", "path_type": "hardlink", "sha256": "de24b714f2abc02e08dd9f734629731391fb4f9e549304db6555cde80df1fa0a", "sha256_in_prefix": "de24b714f2abc02e08dd9f734629731391fb4f9e549304db6555cde80df1fa0a", "size_in_bytes": 7941}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/chatspeak_model.cpython-310.pyc", "path_type": "hardlink", "sha256": "745e9bff8ff7d5090fc3c43f0b7e43becb9cd26cfb12150a3edc6cfba5d1f379", "sha256_in_prefix": "745e9bff8ff7d5090fc3c43f0b7e43becb9cd26cfb12150a3edc6cfba5d1f379", "size_in_bytes": 2467}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/dates.cpython-310.pyc", "path_type": "hardlink", "sha256": "3beae7b330b89a3282e8ed024e95f6ca062593853828f1d229c1f996e9fea3fb", "sha256_in_prefix": "3beae7b330b89a3282e8ed024e95f6ca062593853828f1d229c1f996e9fea3fb", "size_in_bytes": 4271}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/g2p.cpython-310.pyc", "path_type": "hardlink", "sha256": "1f6cd446e5fad8e1709d1f56dd41b7f8bc6684ef37ba2a11fc4c23d53cca30cb", "sha256_in_prefix": "1f6cd446e5fad8e1709d1f56dd41b7f8bc6684ef37ba2a11fc4c23d53cca30cb", "size_in_bytes": 1516}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/numbers.cpython-310.pyc", "path_type": "hardlink", "sha256": "8d67aaf153c14eac5ed2b6bc0e642a785b9a0fe799a55d2c16c8505a51771b74", "sha256_in_prefix": "8d67aaf153c14eac5ed2b6bc0e642a785b9a0fe799a55d2c16c8505a51771b74", "size_in_bytes": 1935}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/plurals.cpython-310.pyc", "path_type": "hardlink", "sha256": "4450ed0b97508709b6e39d18138fb07c81a4d539804a8c03a6e86ca98f09cd68", "sha256_in_prefix": "4450ed0b97508709b6e39d18138fb07c81a4d539804a8c03a6e86ca98f09cd68", "size_in_bytes": 1373}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/t9.cpython-310.pyc", "path_type": "hardlink", "sha256": "d3e9797dc962cd1e7d9329c0da2b17fa88315748c88182e37a18d7ac276bbf40", "sha256_in_prefix": "d3e9797dc962cd1e7d9329c0da2b17fa88315748c88182e37a18d7ac276bbf40", "size_in_bytes": 2059}, {"_path": "Lib/site-packages/pynini/examples/__pycache__/weather.cpython-310.pyc", "path_type": "hardlink", "sha256": "c684a3b58ab47b48c6dd7dc5bb0f5912a785d5a013d461d47f2b634481ce951e", "sha256_in_prefix": "c684a3b58ab47b48c6dd7dc5bb0f5912a785d5a013d461d47f2b634481ce951e", "size_in_bytes": 3004}, {"_path": "Lib/site-packages/pynini/examples/case.py", "path_type": "hardlink", "sha256": "fa1efa735956f8f188c4dfba9668e800d141cf5e452e8fd34b381059e271d94f", "sha256_in_prefix": "fa1efa735956f8f188c4dfba9668e800d141cf5e452e8fd34b381059e271d94f", "size_in_bytes": 2729}, {"_path": "Lib/site-packages/pynini/examples/chatspeak.py", "path_type": "hardlink", "sha256": "c9ad39150cb613caf05c6de4b11dc363b807d2ff521bb9937e6d8b014ff0385e", "sha256_in_prefix": "c9ad39150cb613caf05c6de4b11dc363b807d2ff521bb9937e6d8b014ff0385e", "size_in_bytes": 9293}, {"_path": "Lib/site-packages/pynini/examples/chatspeak_model.py", "path_type": "hardlink", "sha256": "ce0bed0c7dcf73de2d14c02ec10ce62ed77054879f04bfacc3b62a3ce0a31dec", "sha256_in_prefix": "ce0bed0c7dcf73de2d14c02ec10ce62ed77054879f04bfacc3b62a3ce0a31dec", "size_in_bytes": 3066}, {"_path": "Lib/site-packages/pynini/examples/dates.py", "path_type": "hardlink", "sha256": "586f935741c2b42b6cb685321c5311257beed298ecfe0ff2193092dbb3e42fce", "sha256_in_prefix": "586f935741c2b42b6cb685321c5311257beed298ecfe0ff2193092dbb3e42fce", "size_in_bytes": 5233}, {"_path": "Lib/site-packages/pynini/examples/g2p.py", "path_type": "hardlink", "sha256": "d501080950a85202b2ab9e139b6e90dbfc7862b73694a44f174cd7742685aaff", "sha256_in_prefix": "d501080950a85202b2ab9e139b6e90dbfc7862b73694a44f174cd7742685aaff", "size_in_bytes": 2455}, {"_path": "Lib/site-packages/pynini/examples/numbers.py", "path_type": "hardlink", "sha256": "d627cdadc43a3a2548b33e7f21ac1d701ae173173f370c1e292f7d2eccf26bce", "sha256_in_prefix": "d627cdadc43a3a2548b33e7f21ac1d701ae173173f370c1e292f7d2eccf26bce", "size_in_bytes": 4173}, {"_path": "Lib/site-packages/pynini/examples/plurals.py", "path_type": "hardlink", "sha256": "08cbc7c7569e0c26c8267ccb90eef010d12357685573c051a968139987ed2261", "sha256_in_prefix": "08cbc7c7569e0c26c8267ccb90eef010d12357685573c051a968139987ed2261", "size_in_bytes": 2435}, {"_path": "Lib/site-packages/pynini/examples/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini/examples/t9.py", "path_type": "hardlink", "sha256": "7fa4d241020cc2217f92ddd0e629a02d539f6871135cef58e18f891d177dfbd8", "sha256_in_prefix": "7fa4d241020cc2217f92ddd0e629a02d539f6871135cef58e18f891d177dfbd8", "size_in_bytes": 2145}, {"_path": "Lib/site-packages/pynini/examples/weather.py", "path_type": "hardlink", "sha256": "89ac6dec65168a17b5f4914baa3c7c8676653b499fc534c681ba016ffb590fec", "sha256_in_prefix": "89ac6dec65168a17b5f4914baa3c7c8676653b499fc534c681ba016ffb590fec", "size_in_bytes": 3314}, {"_path": "Lib/site-packages/pynini/export/__init__.py", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pynini/export/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "19bd8d7dbfc6393508827dbed069347f1819e997f066b29ad7e6a0d5171d2568", "sha256_in_prefix": "19bd8d7dbfc6393508827dbed069347f1819e997f066b29ad7e6a0d5171d2568", "size_in_bytes": 136}, {"_path": "Lib/site-packages/pynini/export/__pycache__/export.cpython-310.pyc", "path_type": "hardlink", "sha256": "390ca17b062da4f79129049808be24e50a6a980c25bfeac82ed368ba914dcaeb", "sha256_in_prefix": "390ca17b062da4f79129049808be24e50a6a980c25bfeac82ed368ba914dcaeb", "size_in_bytes": 2629}, {"_path": "Lib/site-packages/pynini/export/__pycache__/grm.cpython-310.pyc", "path_type": "hardlink", "sha256": "ab6789111851cc8ee9d169e30cf68727b8ca991c938aa75357ee0f2314978813", "sha256_in_prefix": "ab6789111851cc8ee9d169e30cf68727b8ca991c938aa75357ee0f2314978813", "size_in_bytes": 1982}, {"_path": "Lib/site-packages/pynini/export/__pycache__/grm_example.cpython-310.pyc", "path_type": "hardlink", "sha256": "fa3fed23cfed73b7592192c0319d49964379b24e03f063385c2c5ad304cc6b8a", "sha256_in_prefix": "fa3fed23cfed73b7592192c0319d49964379b24e03f063385c2c5ad304cc6b8a", "size_in_bytes": 529}, {"_path": "Lib/site-packages/pynini/export/__pycache__/multi_grm.cpython-310.pyc", "path_type": "hardlink", "sha256": "f01c8c061b01b8dc3535eb0ab83a39c6d0deb6471761e4040601ac55f4e88cf4", "sha256_in_prefix": "f01c8c061b01b8dc3535eb0ab83a39c6d0deb6471761e4040601ac55f4e88cf4", "size_in_bytes": 3343}, {"_path": "Lib/site-packages/pynini/export/__pycache__/multi_grm_example.cpython-310.pyc", "path_type": "hardlink", "sha256": "21abb07c826b61922f211647aa7d1e10f5e7d1a5359c7fd6d74bbc5184827201", "sha256_in_prefix": "21abb07c826b61922f211647aa7d1e10f5e7d1a5359c7fd6d74bbc5184827201", "size_in_bytes": 576}, {"_path": "Lib/site-packages/pynini/export/export.py", "path_type": "hardlink", "sha256": "3d7e6e909f6f1da54f3870bf660d998142150c0bf53f438590080beef6287e63", "sha256_in_prefix": "3d7e6e909f6f1da54f3870bf660d998142150c0bf53f438590080beef6287e63", "size_in_bytes": 3273}, {"_path": "Lib/site-packages/pynini/export/grm.py", "path_type": "hardlink", "sha256": "2418bf63399eb34da35a2080538123199f1d49e1e9aabbb8cb8c6c907acc7d5e", "sha256_in_prefix": "2418bf63399eb34da35a2080538123199f1d49e1e9aabbb8cb8c6c907acc7d5e", "size_in_bytes": 2368}, {"_path": "Lib/site-packages/pynini/export/grm_example.py", "path_type": "hardlink", "sha256": "d985352e98ad7f64096e9e7cfd551a305c00f1cf71b0c1eeeb6f5ea5e5559d23", "sha256_in_prefix": "d985352e98ad7f64096e9e7cfd551a305c00f1cf71b0c1eeeb6f5ea5e5559d23", "size_in_bytes": 893}, {"_path": "Lib/site-packages/pynini/export/multi_grm.py", "path_type": "hardlink", "sha256": "8dbf0805fef19ccc817c52fad0dc05913599227e1b9e7a81697e47843a399741", "sha256_in_prefix": "8dbf0805fef19ccc817c52fad0dc05913599227e1b9e7a81697e47843a399741", "size_in_bytes": 3786}, {"_path": "Lib/site-packages/pynini/export/multi_grm_example.py", "path_type": "hardlink", "sha256": "e716130790d1ce14d90c13413f6f7312190d89d3a480e0bb7b952a43154eb225", "sha256_in_prefix": "e716130790d1ce14d90c13413f6f7312190d89d3a480e0bb7b952a43154eb225", "size_in_bytes": 955}, {"_path": "Lib/site-packages/pynini/export/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini/lib/__init__.py", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "44f467993a1ae33a43896f50547300a46c7ae396230a5bbfb89adae0d060fff1", "sha256_in_prefix": "44f467993a1ae33a43896f50547300a46c7ae396230a5bbfb89adae0d060fff1", "size_in_bytes": 133}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/byte.cpython-310.pyc", "path_type": "hardlink", "sha256": "69b8f02cf3b455d89386ae4e09cac70227c65b861a591491f26636f65f160d81", "sha256_in_prefix": "69b8f02cf3b455d89386ae4e09cac70227c65b861a591491f26636f65f160d81", "size_in_bytes": 848}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/edit_transducer.cpython-310.pyc", "path_type": "hardlink", "sha256": "a62fff0c0a3372b0062b309d2bf7d80800f61c3dbdc91c4920d10dc3e5bbba6f", "sha256_in_prefix": "a62fff0c0a3372b0062b309d2bf7d80800f61c3dbdc91c4920d10dc3e5bbba6f", "size_in_bytes": 8041}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/features.cpython-310.pyc", "path_type": "hardlink", "sha256": "d7c029920512194ad3ed00f764d06c9dd6ac59677d3248e078fd4109fc1e3e44", "sha256_in_prefix": "d7c029920512194ad3ed00f764d06c9dd6ac59677d3248e078fd4109fc1e3e44", "size_in_bytes": 11150}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/paradigms.cpython-310.pyc", "path_type": "hardlink", "sha256": "0bd30ecd170d093c7a3508487fe72c34a85b88a9d4897d6a5b1c5a90f0288f30", "sha256_in_prefix": "0bd30ecd170d093c7a3508487fe72c34a85b88a9d4897d6a5b1c5a90f0288f30", "size_in_bytes": 16366}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/pynutil.cpython-310.pyc", "path_type": "hardlink", "sha256": "7f4d70f98740fa47b0e409aac7b594f3e832fa2970256568da195249ee1df102", "sha256_in_prefix": "7f4d70f98740fa47b0e409aac7b594f3e832fa2970256568da195249ee1df102", "size_in_bytes": 1750}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/rewrite.cpython-310.pyc", "path_type": "hardlink", "sha256": "43000cc9349cff49858edf36c54ecd554f93e4190dea37bc80adc0cf27deb24b", "sha256_in_prefix": "43000cc9349cff49858edf36c54ecd554f93e4190dea37bc80adc0cf27deb24b", "size_in_bytes": 10499}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/rule_cascade.cpython-310.pyc", "path_type": "hardlink", "sha256": "8b8f7d2f0be48d634d4c1fbf54b1fa9200af9c09ce55a078a532c90caedb194f", "sha256_in_prefix": "8b8f7d2f0be48d634d4c1fbf54b1fa9200af9c09ce55a078a532c90caedb194f", "size_in_bytes": 6534}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/tagger.cpython-310.pyc", "path_type": "hardlink", "sha256": "b5f09b8622dd51e2b62ca21394f6738fcca7d40ba45de533ad56e8378bbbc83c", "sha256_in_prefix": "b5f09b8622dd51e2b62ca21394f6738fcca7d40ba45de533ad56e8378bbbc83c", "size_in_bytes": 2314}, {"_path": "Lib/site-packages/pynini/lib/__pycache__/utf8.cpython-310.pyc", "path_type": "hardlink", "sha256": "8fb894c4ae04ab5396dc374d979cd63a55afa8150a3d4b6cdab87ed5a6f16b10", "sha256_in_prefix": "8fb894c4ae04ab5396dc374d979cd63a55afa8150a3d4b6cdab87ed5a6f16b10", "size_in_bytes": 1788}, {"_path": "Lib/site-packages/pynini/lib/byte.py", "path_type": "hardlink", "sha256": "c1ca3eede319017eeb2cb9657730cadfe942a28f9e506a183436d4bf91b2eecf", "sha256_in_prefix": "c1ca3eede319017eeb2cb9657730cadfe942a28f9e506a183436d4bf91b2eecf", "size_in_bytes": 1624}, {"_path": "Lib/site-packages/pynini/lib/edit_transducer.py", "path_type": "hardlink", "sha256": "efe60f5c6b595dd2db85d3489baf39d793310ab66d2212ffe0f46a15809b6a77", "sha256_in_prefix": "efe60f5c6b595dd2db85d3489baf39d793310ab66d2212ffe0f46a15809b6a77", "size_in_bytes": 8667}, {"_path": "Lib/site-packages/pynini/lib/features.py", "path_type": "hardlink", "sha256": "48118545184502176db5798297e11f17a0ad0037ec347a7dfaa2ec33df5e4197", "sha256_in_prefix": "48118545184502176db5798297e11f17a0ad0037ec347a7dfaa2ec33df5e4197", "size_in_bytes": 9909}, {"_path": "Lib/site-packages/pynini/lib/paradigms.py", "path_type": "hardlink", "sha256": "afccd74267be9b81e8932017d04237a256d2647adaab43ecd6339fcf61d4cbad", "sha256_in_prefix": "afccd74267be9b81e8932017d04237a256d2647adaab43ecd6339fcf61d4cbad", "size_in_bytes": 18822}, {"_path": "Lib/site-packages/pynini/lib/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pynini/lib/pynutil.py", "path_type": "hardlink", "sha256": "980ff46d584589978dbe2be88e2b39b5a31458c4079de78989428e3955ebf033", "sha256_in_prefix": "980ff46d584589978dbe2be88e2b39b5a31458c4079de78989428e3955ebf033", "size_in_bytes": 2253}, {"_path": "Lib/site-packages/pynini/lib/rewrite.py", "path_type": "hardlink", "sha256": "380521d27f7d86e6fc7630adab1ee2d3f6341630d1b328e3ee0a1c8e15710e5d", "sha256_in_prefix": "380521d27f7d86e6fc7630adab1ee2d3f6341630d1b328e3ee0a1c8e15710e5d", "size_in_bytes": 12433}, {"_path": "Lib/site-packages/pynini/lib/rule_cascade.py", "path_type": "hardlink", "sha256": "674ea525e91ebcd8ae9ce3c3a10b61310b6a1c14d709c1d1d23fb5acee97f9b3", "sha256_in_prefix": "674ea525e91ebcd8ae9ce3c3a10b61310b6a1c14d709c1d1d23fb5acee97f9b3", "size_in_bytes": 7743}, {"_path": "Lib/site-packages/pynini/lib/tagger.py", "path_type": "hardlink", "sha256": "7256107b465b46968cb1614234e94775d9a99e17b3c1a3eed6e95d60cd8eb5a4", "sha256_in_prefix": "7256107b465b46968cb1614234e94775d9a99e17b3c1a3eed6e95d60cd8eb5a4", "size_in_bytes": 2557}, {"_path": "Lib/site-packages/pynini/lib/utf8.py", "path_type": "hardlink", "sha256": "5f5ed6582fbbca5d1a8dca3cb962f78943658f0d3cea1e8df4925437265a1c4b", "sha256_in_prefix": "5f5ed6582fbbca5d1a8dca3cb962f78943658f0d3cea1e8df4925437265a1c4b", "size_in_bytes": 3956}, {"_path": "Lib/site-packages/pynini/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Lib/site-packages/pywrapfst/__init__.py", "path_type": "hardlink", "sha256": "004b691d8c8360b5c5accfdeb5835e02c0a5efb7cd6dfe4604888033c7b25fcd", "sha256_in_prefix": "004b691d8c8360b5c5accfdeb5835e02c0a5efb7cd6dfe4604888033c7b25fcd", "size_in_bytes": 25}, {"_path": "Lib/site-packages/pywrapfst/__init__.pyi", "path_type": "hardlink", "sha256": "ce1c1b815caf9155a27d4607e0373902d9f7112cbc792497051e79bff2186d7c", "sha256_in_prefix": "ce1c1b815caf9155a27d4607e0373902d9f7112cbc792497051e79bff2186d7c", "size_in_bytes": 23938}, {"_path": "Lib/site-packages/pywrapfst/__pycache__/__init__.cpython-310.pyc", "path_type": "hardlink", "sha256": "70ab9ed016e97b04ec8f893744177bc499bd805fa39a8a2ac37e394c38ecbdfa", "sha256_in_prefix": "70ab9ed016e97b04ec8f893744177bc499bd805fa39a8a2ac37e394c38ecbdfa", "size_in_bytes": 159}, {"_path": "Lib/site-packages/pywrapfst/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "Library/include/_pywrapfst.h", "path_type": "hardlink", "sha256": "6571828f31d08a27b9f8006cb6f6d9d62aeb41c536a095378b35f73f4290d72d", "sha256_in_prefix": "6571828f31d08a27b9f8006cb6f6d9d62aeb41c536a095378b35f73f4290d72d", "size_in_bytes": 9718}], "paths_version": 1}, "requested_spec": "pynini==2.1.5", "sha256": "32c5c5730c541e0cdfac0363d6092b2e89392df11c049392393f060268019d5b", "size": 816844, "subdir": "win-64", "timestamp": 1696661584000, "url": "https://conda.anaconda.org/conda-forge/win-64/pynini-2.1.5-py310h232114e_6.conda", "version": "2.1.5"}